import React from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { Link } from 'react-router-dom';
import { TrackingStatus } from '../../../../types/Workflow';

interface MocStatusSummaryProps {
    mocStatusSummary: {
        totalMOC: number;
        inProgress: number;
        complete: number;
        cancel: number;
        overdue: number;
        backlog: number;
        onTracking: number;
    };
}

const mocStatusSummary = ({ mocStatusSummary }: Readonly<MocStatusSummaryProps>) => {
    // Đồng bộ height giữa 2 chart bằng cách sử dụng cùng height và điều chỉnh barHeight
    const chartHeight = 300; // Height cố định cho cả 2 chart
    const leftCategories = 4; // Total, In Progress, Complete, Cancel
    const rightCategories = 3; // On-Tracking, Overdue, Backlog

    // Tính barHeight để các cột có kích thước tương đương
    const leftBarHeight = '50px'; // Cho 4 cột
    const rightBarHeight = '65px'; // Cho 3 cột, lớn hơn để cân bằng với chart trái

    // Chart options cho biểu đồ bên trái (Total, In Progress, Complete, Cancel)
    const leftChartOptions: ApexOptions = {
        chart: {
            type: 'bar',
            toolbar: {
                show: false,
            },
        },
        plotOptions: {
            bar: {
                horizontal: true,
                barHeight: leftBarHeight,
                distributed: true,
            },
        },
        dataLabels: {
            enabled: true,
            style: {
                colors: ['#fff'],
                fontSize: '14px',
                fontWeight: 'bold',
            },
        },
        xaxis: {
            categories: ['Total', 'In Progress', 'Completed', 'Cancelled'],
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        yaxis: {
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        colors: ['#6f42c1', '#007bff', '#28a745', '#ffc107'],
        legend: {
            show: false,
        },
        grid: {
            show: true,
            borderColor: '#e9ecef',
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toString();
                },
            },
        },
    };

    const leftChartSeries = [
        {
            name: 'MOC Count',
            data: [
                mocStatusSummary?.totalMOC,
                mocStatusSummary?.inProgress,
                mocStatusSummary?.complete,
                mocStatusSummary?.cancel,
            ],
        },
    ];

    // Chart options cho biểu đồ bên phải (Unhealthy MOC)
    const rightChartOptions: ApexOptions = {
        chart: {
            type: 'bar',
            toolbar: {
                show: false,
            },
        },
        plotOptions: {
            bar: {
                horizontal: true,
                barHeight: rightBarHeight,
                distributed: true,
            },
        },
        dataLabels: {
            enabled: true,
            style: {
                colors: ['#fff'],
                fontSize: '14px',
                fontWeight: 'bold',
            },
        },
        xaxis: {
            categories: ['On-Tracking', 'Overdue', 'Backlog'],
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        yaxis: {
            labels: {
                style: {
                    fontSize: '12px',
                },
            },
        },
        colors: ['#007bff', '#dc3545', '#343a40'],
        legend: {
            show: false,
        },
        grid: {
            show: true,
            borderColor: '#e9ecef',
        },
        tooltip: {
            y: {
                formatter: function (val) {
                    return val.toString();
                },
            },
        },
    };

    const rightChartSeries = [
        {
            name: 'MOC Count',
            data: [mocStatusSummary?.onTracking, mocStatusSummary?.overdue, mocStatusSummary?.backlog],
        },
    ];

    return (
        <div className="row">
            <div className="col-12 col-md-6">
                <div className="card">
                    <div className="card-body">
                        {/* Header */}
                        <div className="tw-mb-4 tw-w-full">
                            <div className="tw-text-center">
                                <h4 className="tw-text-gray-600 tw-font-bold tw-text-xl tw-mb-3">MOC Status Summary</h4>
                            </div>

                            <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 md:tw-grid-cols-4 tw-gap-3 tw-mx-auto tw-w-full">
                                <Link
                                    to={`/workflowInstance`}
                                    target="_blank"
                                    className="tw-border-2 tw-border-purple-500 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center"
                                >
                                    <div className="tw-text-purple-500 tw-font-bold tw-text-sm tw-bg-purple-100 tw-py-2">
                                        Total
                                    </div>
                                    <div className="tw-text-2xl tw-font-bold tw-text-purple-500">
                                        {mocStatusSummary?.totalMOC}
                                    </div>
                                </Link>
                                <Link
                                    to={`/workflowInstance?tracking_status=${[
                                        TrackingStatus.ON_TRACKING,
                                        TrackingStatus.OVERDUE,
                                        TrackingStatus.BACKLOG,
                                    ].join(',')}`}
                                    target="_blank"
                                    className="tw-border-2 tw-border-blue-500 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center"
                                >
                                    <div className="tw-text-blue-500 tw-font-bold tw-text-sm tw-bg-blue-100 tw-py-2">
                                        In Progress
                                    </div>
                                    <div className="tw-text-2xl tw-font-bold tw-text-blue-500">
                                        {mocStatusSummary?.inProgress}
                                    </div>
                                </Link>

                                <Link
                                    to={`/workflowInstance?tracking_status=${TrackingStatus.COMPLETED}`}
                                    target="_blank"
                                    className="tw-border-2 tw-border-green-500 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center"
                                >
                                    <div className="tw-text-green-500 tw-font-bold tw-text-sm tw-bg-green-100 tw-py-2">
                                        Completed
                                    </div>
                                    <div className="tw-text-2xl tw-font-bold tw-text-green-500">
                                        {mocStatusSummary?.complete}
                                    </div>
                                </Link>

                                <Link
                                    to={`/workflowInstance?tracking_status=${TrackingStatus.CANCELLED}`}
                                    target="_blank"
                                    className="tw-border-2 tw-border-yellow-500 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center"
                                >
                                    <div className="tw-text-yellow-500 tw-font-bold tw-text-sm tw-bg-yellow-100 tw-py-2">
                                        Cancelled
                                    </div>
                                    <div className="tw-text-2xl tw-font-bold tw-text-yellow-500">
                                        {mocStatusSummary?.cancel}
                                    </div>
                                </Link>
                            </div>
                        </div>
                        <div className="tw-w-full">
                            <Chart options={leftChartOptions} series={leftChartSeries} type="bar" height={chartHeight} />
                        </div>
                    </div>
                </div>
            </div>
            <div className="col-12 col-md-6">
                <div className="card">
                    <div className="card-body">
                        <div className="tw-w-full tw-text-center">
                            <h4 className="tw-text-gray-600 tw-font-bold tw-text-xl tw-mb-4">Unhealthy MOC</h4>
                        </div>
                        <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 md:tw-grid-cols-4 tw-gap-3 tw-mx-auto tw-w-full">
                            <div className="mobile-460:tw-hidden tw-border-red-500  tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center">
                                <div className="tw-text-red-500 tw-font-bold tw-text-sm  tw-py-2"></div>
                                <div className="tw-text-2xl tw-font-bold tw-text-red-500"></div>
                            </div>
                            <Link
                                to={`/workflowInstance?tracking_status=${TrackingStatus.OVERDUE}`}
                                target="_blank"
                                className="tw-border-2 tw-border-red-500 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center"
                            >
                                <div className="tw-text-red-500 tw-font-bold tw-text-sm tw-bg-red-100 tw-py-2">
                                    Overdue
                                </div>
                                <div className="tw-text-2xl tw-font-bold tw-text-red-500">
                                    {mocStatusSummary?.overdue}
                                </div>
                            </Link>
                            <Link
                                to={`/workflowInstance?tracking_status=${TrackingStatus.BACKLOG}`}
                                target="_blank"
                                className="tw-border-2 tw-border-gray-700 tw-bg-gray-50 tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center"
                            >
                                <div className="tw-text-gray-700 tw-font-bold tw-text-sm tw-bg-gray-100 tw-py-2">
                                    Backlog
                                </div>
                                <div className="tw-text-2xl tw-font-bold tw-text-gray-700">
                                    {mocStatusSummary?.backlog}
                                </div>
                            </Link>
                            <div className="tw-text-center tw-pb-3 tw-min-h-20 tw-justify-center mobile-460:tw-hidden">
                                <div className="tw-text-red-500 tw-font-bold tw-text-sm tw-py-2"></div>
                                <div className="tw-text-2xl tw-font-bold tw-text-red-500"></div>
                            </div>
                        </div>
                        <div className="tw-w-full">
                            <Chart options={rightChartOptions} series={rightChartSeries} type="bar" height={chartHeight} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default mocStatusSummary;
